#!/usr/bin/env python3
"""
简单的Excel翻译工具 - MVP版本
用于快速验证Excel翻译的想法，不使用LangGraph
"""

import os
import json
import xlrd
import xlwt
from pathlib import Path
from typing import Dict, List
from dataclasses import dataclass
from dotenv import load_dotenv

# 导入xlutils用于保留格式
try:
    from xlutils.copy import copy as xlutils_copy
    XLUTILS_AVAILABLE = True
except ImportError:
    print("警告：xlutils不可用，将无法保留Excel格式。请安装: pip install xlutils")
    XLUTILS_AVAILABLE = False

load_dotenv(override=True)

# 如果有OpenAI库，使用它；否则使用简单的HTTP请求
try:
    from langchain_openai import ChatOpenAI
    from langchain_core.messages import HumanMessage, SystemMessage
    USE_LANGCHAIN = True
except ImportError:
    import requests
    USE_LANGCHAIN = False

@dataclass
class SimpleExcelTranslatorConfig:
    """简单Excel翻译器配置"""
    model_name: str = ""
    api_key: str = ""
    base_url: str = ""
    target_language: str = "中文"
    batch_size: int = 10
    
    def __post_init__(self):
        if not self.api_key:
            self.api_key = os.getenv("OPENAI_API_KEY", "")
        if not self.base_url:
            self.base_url = os.getenv("OPENAI_BASE_URL", "")
        if not self.model_name:
            self.model_name = os.getenv("OPENAI_MODEL", "")

class SimpleExcelTranslator:
    """简单Excel翻译器"""
    
    def __init__(self, config: SimpleExcelTranslatorConfig):
        self.config = config
        
        if USE_LANGCHAIN and config.api_key:
            try:
                self.llm = ChatOpenAI(
                    model=config.model_name,
                    temperature=0.1
                )
            except Exception as e:
                print(f"初始化LangChain失败: {e}")
                self.llm = None
        else:
            self.llm = None
    
    def analyze_excel(self, file_path: str) -> Dict:
        """分析Excel文件，提取需要翻译的文本"""
        print(f"正在分析Excel文件: {file_path}")
        
        try:
            workbook = xlrd.open_workbook(file_path)
            sheet_names = workbook.sheet_names()
            
            text_cells = []
            
            # 遍历所有工作表
            for sheet_name in sheet_names:
                sheet = workbook.sheet_by_name(sheet_name)
                print(f"  分析工作表: {sheet_name} ({sheet.nrows}行 x {sheet.ncols}列)")
                
                # 遍历所有单元格
                for row in range(sheet.nrows):
                    for col in range(sheet.ncols):
                        try:
                            value = sheet.cell_value(row, col)
                            if isinstance(value, str) and len(value.strip()) > 0:
                                # 简单判断是否需要翻译（非纯数字、非公式）
                                clean_value = value.strip()
                                if (not clean_value.replace('.', '').replace(',', '').replace('-', '').replace('%', '').isdigit() 
                                    and not clean_value.startswith('=')):
                                    text_cells.append({
                                        "sheet_name": sheet_name,
                                        "row": row,
                                        "col": col,
                                        "original_text": clean_value,
                                        "cell_ref": f"{sheet_name}!{chr(65 + col)}{row + 1}"
                                    })
                        except Exception:
                            continue
            
            print(f"找到 {len(text_cells)} 个需要翻译的文本单元格")
            
            return {
                "sheet_names": sheet_names,
                "text_cells": text_cells
            }
            
        except Exception as e:
            print(f"分析Excel文件失败: {e}")
            return {"error": str(e)}
    
    def translate_texts_simple(self, texts: List[str]) -> List[str]:
        """简单的文本翻译（模拟翻译）"""
        # 这是一个简单的模拟翻译，实际应用中应该调用真正的翻译API
        translation_map = {
            "PROJECT ECONOMIC ANALYSIS": "项目经济分析",
            "PROJECT": "项目",
            "COAL FIRED POWER PLANT": "燃煤发电厂",
            "Project Investment Cost": "项目投资成本",
            "Assessment Index": "评估指标",
            "Hard Costs": "硬成本",
            "Project IRR": "项目内部收益率",
            "Unit 1 & Unit 2": "1号机组和2号机组",
            "Equity IRR": "股权内部收益率",
            "Payback Period": "投资回收期",
            "Year": "年",
            "DSCR max": "最大偿债覆盖率",
            "DSCR min": "最小偿债覆盖率",
            "Tariff Structure": "电价结构",
            "Fixed Capacity Charge": "固定容量费",
            "Soft Costs": "软成本",
            "Development Fee": "开发费",
            "Owners Engineer Cost": "业主工程师费用",
            "Construction": "建设",
            "Operation": "运营",
            "Financing": "融资",
            "Depreciation": "折旧",
            "Assumption": "假设",
            "All figures in USD '000 unless stated otherwise": "除非另有说明，所有数字均以千美元为单位",
            "Investment": "投资",
            "Date": "日期",
            "Month": "月",
            "Years": "年",
            "MW": "兆瓦",
            "MWh": "兆瓦时",
            "USD/ton": "美元/吨",
            "Income Statement": "损益表",
            "Cash Flow": "现金流",
            "Balance Sheet": "资产负债表",
            "Dividends": "股息",
            "Period Start": "期间开始",
            "Period End": "期间结束"
        }
        
        translated = []
        for text in texts:
            # 先查找直接匹配
            if text in translation_map:
                translated.append(translation_map[text])
            else:
                # 简单的关键词替换
                result = text
                for en, zh in translation_map.items():
                    if en.lower() in text.lower():
                        result = result.replace(en, zh)
                translated.append(result)
        
        return translated
    
    def translate_texts_with_llm(self, texts: List[str]) -> List[str]:
        """使用LLM翻译文本"""
        if not self.llm or not texts:
            return self.translate_texts_simple(texts)

        try:
            # 构建强化的翻译提示
            texts_json = json.dumps(texts, ensure_ascii=False, indent=2)

            system_prompt = f"""你是一个专业的Excel文档翻译专家。你的任务是将英文财务和工程术语准确翻译成{self.config.target_language}。

核心要求：
1. 只输出翻译结果，不要任何解释、说明或额外文字
2. 保持专业术语的准确性（财务、工程、项目管理术语）
3. 保持原文的格式和结构（包括标点符号、括号、数字等）
4. 专有名词（公司名、地名、产品名）可保持英文
5. 必须返回标准JSON数组格式，与输入顺序完全一致

翻译原则：
- PROJECT → 项目
- ANALYSIS → 分析
- COST → 成本
- INVESTMENT → 投资
- REVENUE → 收入
- CASH FLOW → 现金流
- BALANCE SHEET → 资产负债表
- INCOME STATEMENT → 损益表
- IRR → 内部收益率
- NPV → 净现值
- CAPEX → 资本支出
- OPEX → 运营支出

输出格式：严格的JSON数组，例如：["翻译1", "翻译2", "翻译3"]"""

            user_prompt = f"""翻译以下文本到{self.config.target_language}，只输出JSON数组：

{texts_json}

输出："""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            response = self.llm.invoke(messages)
            response_content = response.content.strip()

            # 清理响应内容，移除可能的markdown标记
            if response_content.startswith('```json'):
                response_content = response_content[7:]
            if response_content.endswith('```'):
                response_content = response_content[:-3]
            response_content = response_content.strip()

            try:
                # 尝试解析JSON响应
                translated_texts = json.loads(response_content)
                if isinstance(translated_texts, list) and len(translated_texts) == len(texts):
                    return translated_texts
                elif isinstance(translated_texts, list):
                    # 如果长度不匹配，补齐或截断
                    result = translated_texts[:len(texts)]
                    while len(result) < len(texts):
                        result.append(texts[len(result)])  # 用原文补齐
                    return result
                else:
                    # 如果不是列表，尝试转换
                    return [str(translated_texts)]

            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"响应内容: {response_content[:200]}...")

                # 尝试从响应中提取翻译内容
                lines = response_content.split('\n')
                translations = []

                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('{') and not line.startswith('['):
                        # 移除可能的引号和逗号
                        clean_line = line.strip('"\'`,')
                        if clean_line:
                            translations.append(clean_line)

                if len(translations) >= len(texts):
                    return translations[:len(texts)]
                else:
                    # 如果提取的翻译不够，用简单翻译补充
                    print("提取的翻译不完整，使用简单翻译补充")
                    return self.translate_texts_simple(texts)

        except Exception as e:
            print(f"LLM翻译失败，使用简单翻译: {e}")
            return self.translate_texts_simple(texts)
    
    def translate_batch(self, text_cells: List[Dict]) -> List[Dict]:
        """批量翻译文本"""
        if not text_cells:
            return []
        
        print(f"开始翻译 {len(text_cells)} 个文本单元格...")
        
        translated_cells = []
        batch_size = self.config.batch_size
        
        # 分批处理
        for i in range(0, len(text_cells), batch_size):
            batch = text_cells[i:i + batch_size]
            print(f"  翻译批次 {i//batch_size + 1}/{(len(text_cells) + batch_size - 1)//batch_size}")
            
            # 准备批量翻译的文本
            texts_to_translate = [cell["original_text"] for cell in batch]
            
            # 调用翻译
            if self.config.api_key and self.llm:
                translated_texts = self.translate_texts_with_llm(texts_to_translate)
            else:
                print("    使用简单翻译（未配置API密钥）")
                translated_texts = self.translate_texts_simple(texts_to_translate)
            
            # 组合结果
            for j, cell in enumerate(batch):
                translated_cell = {
                    **cell,
                    "translated_text": translated_texts[j] if j < len(translated_texts) else cell["original_text"]
                }
                translated_cells.append(translated_cell)
        
        print(f"翻译完成，共处理 {len(translated_cells)} 个单元格")
        return translated_cells
    
    def _get_out_cell(self, out_sheet, col_index, row_index):
        """获取xlwt内部单元格表示（用于保留格式）"""
        row = out_sheet._Worksheet__rows.get(row_index)
        if not row:
            return None
        cell = row._Row__cells.get(col_index)
        return cell

    def _set_out_cell_with_format(self, out_sheet, col, row, value):
        """在保留格式的情况下更改单元格值"""
        # 获取之前的单元格格式
        previous_cell = self._get_out_cell(out_sheet, col, row)

        # 写入新值
        out_sheet.write(row, col, value)

        # 恢复格式
        if previous_cell:
            new_cell = self._get_out_cell(out_sheet, col, row)
            if new_cell:
                new_cell.xf_idx = previous_cell.xf_idx

    def save_translated_excel(self, input_file: str, output_file: str, translated_cells: List[Dict]) -> bool:
        """保存翻译后的Excel文件（保留格式）"""
        print(f"正在保存翻译结果到: {output_file}")

        try:
            if XLUTILS_AVAILABLE:
                # 使用xlutils保留格式
                print("  使用xlutils保留Excel格式...")

                # 读取原始文件（启用格式信息）
                original_workbook = xlrd.open_workbook(input_file, formatting_info=True)

                # 使用xlutils复制工作簿（保留格式）
                new_workbook = xlutils_copy(original_workbook)

                # 创建翻译映射
                translation_map = {}
                for cell in translated_cells:
                    key = f"{cell['sheet_name']}_{cell['row']}_{cell['col']}"
                    translation_map[key] = cell['translated_text']

                # 更新翻译的单元格
                for sheet_idx, sheet_name in enumerate(original_workbook.sheet_names()):
                    if sheet_idx < len(new_workbook._Workbook__worksheets):
                        new_sheet = new_workbook.get_sheet(sheet_idx)
                        print(f"  处理工作表: {sheet_name}")

                        # 只更新需要翻译的单元格
                        for cell in translated_cells:
                            if cell['sheet_name'] == sheet_name:
                                try:
                                    # 使用格式保留的方式写入
                                    self._set_out_cell_with_format(
                                        new_sheet,
                                        cell['col'],
                                        cell['row'],
                                        cell['translated_text']
                                    )
                                except Exception as e:
                                    print(f"    警告：更新单元格 {cell['cell_ref']} 失败: {e}")
                                    # 如果格式保留失败，使用普通写入
                                    try:
                                        new_sheet.write(cell['row'], cell['col'], cell['translated_text'])
                                    except Exception as e2:
                                        print(f"    错误：普通写入也失败: {e2}")

                # 保存文件
                new_workbook.save(output_file)
                print(f"翻译完成！文件已保存到: {output_file}")
                return True

            else:
                # 回退到原始方法（不保留格式）
                print("  xlutils不可用，使用基本方法（不保留格式）...")
                return self._save_translated_excel_basic(input_file, output_file, translated_cells)

        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            print("  尝试使用基本方法...")
            return self._save_translated_excel_basic(input_file, output_file, translated_cells)

    def _save_translated_excel_basic(self, input_file: str, output_file: str, translated_cells: List[Dict]) -> bool:
        """基本的Excel保存方法（不保留格式）"""
        try:
            # 读取原始文件
            original_workbook = xlrd.open_workbook(input_file)

            # 创建新的工作簿
            new_workbook = xlwt.Workbook(encoding='utf-8')

            # 创建翻译映射
            translation_map = {}
            for cell in translated_cells:
                key = f"{cell['sheet_name']}_{cell['row']}_{cell['col']}"
                translation_map[key] = cell['translated_text']

            # 复制所有工作表
            for sheet_name in original_workbook.sheet_names():
                original_sheet = original_workbook.sheet_by_name(sheet_name)
                new_sheet = new_workbook.add_sheet(sheet_name)

                print(f"  处理工作表: {sheet_name}")

                # 复制所有单元格
                for row in range(original_sheet.nrows):
                    for col in range(original_sheet.ncols):
                        try:
                            original_value = original_sheet.cell_value(row, col)

                            # 检查是否有翻译
                            key = f"{sheet_name}_{row}_{col}"
                            if key in translation_map:
                                new_value = translation_map[key]
                            else:
                                new_value = original_value

                            # 写入新单元格
                            if isinstance(new_value, str):
                                new_sheet.write(row, col, new_value)
                            elif isinstance(new_value, (int, float)):
                                new_sheet.write(row, col, new_value)
                            else:
                                new_sheet.write(row, col, str(new_value))

                        except Exception as e:
                            print(f"    警告：复制单元格 {row},{col} 失败: {e}")
                            continue

            # 保存文件
            new_workbook.save(output_file)
            print(f"翻译完成！文件已保存到: {output_file}")
            return True

        except Exception as e:
            print(f"基本保存方法也失败: {e}")
            return False
    
    def translate_excel(self, input_file: str, output_file: str) -> Dict:
        """翻译Excel文件的主入口"""
        print(f"开始翻译Excel文件...")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"目标语言: {self.config.target_language}")
        
        # 1. 分析Excel文件
        analysis_result = self.analyze_excel(input_file)
        if "error" in analysis_result:
            return {"success": False, "error": analysis_result["error"]}
        
        # 2. 翻译文本
        translated_cells = self.translate_batch(analysis_result["text_cells"])
        
        # 3. 保存结果
        success = self.save_translated_excel(input_file, output_file, translated_cells)
        
        return {
            "success": success,
            "sheet_count": len(analysis_result["sheet_names"]),
            "translated_cell_count": len(translated_cells),
            "output_file": output_file
        }

def main():
    """主函数 - 演示用法"""
    # 配置
    config = SimpleExcelTranslatorConfig(
        target_language="中文",
        batch_size=5  # 小批量测试
    )
    
    # 创建翻译器
    translator = SimpleExcelTranslator(config)
    
    # 文件路径
    input_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model.xls"
    output_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model_translated.xls"
    
    # 检查输入文件
    if not Path(input_file).exists():
        print(f"错误：输入文件不存在: {input_file}")
        return
    
    # 执行翻译
    result = translator.translate_excel(input_file, output_file)
    
    # 输出结果
    print("\n=== 翻译结果 ===")
    print(f"成功: {result.get('success', False)}")
    if result.get('error'):
        print(f"错误: {result['error']}")
    else:
        print(f"工作表数量: {result.get('sheet_count', 0)}")
        print(f"翻译的单元格数量: {result.get('translated_cell_count', 0)}")
        print(f"输出文件: {result.get('output_file', 'N/A')}")

if __name__ == "__main__":
    main()
