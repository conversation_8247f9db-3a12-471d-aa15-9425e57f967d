#!/usr/bin/env python3
"""
验证Excel翻译结果
"""

import xlrd
from pathlib import Path

def compare_excel_files(original_file: str, translated_file: str):
    """比较原始文件和翻译文件"""
    print(f"比较文件:")
    print(f"  原始文件: {original_file}")
    print(f"  翻译文件: {translated_file}")
    
    # 检查文件是否存在
    if not Path(original_file).exists():
        print(f"错误：原始文件不存在: {original_file}")
        return
    
    if not Path(translated_file).exists():
        print(f"错误：翻译文件不存在: {translated_file}")
        return
    
    try:
        # 读取两个文件
        original_wb = xlrd.open_workbook(original_file)
        translated_wb = xlrd.open_workbook(translated_file)
        
        print(f"\n工作表数量:")
        print(f"  原始文件: {len(original_wb.sheet_names())}")
        print(f"  翻译文件: {len(translated_wb.sheet_names())}")
        
        # 比较第一个有内容的工作表
        sheet_name = "Summary"  # 选择Summary工作表进行比较
        
        if sheet_name in original_wb.sheet_names() and sheet_name in translated_wb.sheet_names():
            original_sheet = original_wb.sheet_by_name(sheet_name)
            translated_sheet = translated_wb.sheet_by_name(sheet_name)
            
            print(f"\n比较工作表: {sheet_name}")
            print(f"原始文件尺寸: {original_sheet.nrows}行 x {original_sheet.ncols}列")
            print(f"翻译文件尺寸: {translated_sheet.nrows}行 x {translated_sheet.ncols}列")
            
            # 比较前10行的文本内容
            print(f"\n翻译对比示例（前10行）:")
            print("-" * 80)
            
            differences = 0
            for row in range(min(10, original_sheet.nrows, translated_sheet.nrows)):
                for col in range(min(5, original_sheet.ncols, translated_sheet.ncols)):  # 只看前5列
                    try:
                        original_value = original_sheet.cell_value(row, col)
                        translated_value = translated_sheet.cell_value(row, col)
                        
                        # 只比较字符串类型的值
                        if isinstance(original_value, str) and isinstance(translated_value, str):
                            if original_value.strip() != translated_value.strip() and len(original_value.strip()) > 0:
                                cell_ref = f"{chr(65 + col)}{row + 1}"
                                print(f"{cell_ref:4} | 原文: {original_value[:50]}")
                                print(f"     | 译文: {translated_value[:50]}")
                                print("-" * 80)
                                differences += 1
                                
                                if differences >= 10:  # 只显示前10个差异
                                    break
                    except Exception as e:
                        continue
                
                if differences >= 10:
                    break
            
            if differences == 0:
                print("在前10行中没有发现翻译差异（可能使用了简单映射）")
            else:
                print(f"发现 {differences} 个翻译差异")
        
        else:
            print(f"工作表 '{sheet_name}' 在其中一个文件中不存在")
            
    except Exception as e:
        print(f"比较文件时出错: {e}")

def main():
    """主函数"""
    original_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model.xls"
    translated_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model_translated.xls"
    
    compare_excel_files(original_file, translated_file)

if __name__ == "__main__":
    main()
