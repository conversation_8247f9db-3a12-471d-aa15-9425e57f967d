#!/usr/bin/env python3
"""
基于LangGraph的Excel翻译工作流 - MVP版本
展示如何使用LangGraph构建Excel翻译的状态机工作流
"""

import os
import json
import xlrd
import xlwt
from pathlib import Path
from typing import Dict, List, TypedDict, Annotated, Literal
from dataclasses import dataclass
import operator

# LangGraph相关导入
try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.memory import MemorySaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    print("LangGraph不可用，请安装: pip install langgraph")
    LANGGRAPH_AVAILABLE = False

# 状态定义
class ExcelTranslationState(TypedDict):
    """Excel翻译状态"""
    # 输入参数
    input_file: str
    output_file: str
    target_language: str
    
    # 工作流状态
    current_step: Literal["analyze", "translate", "save", "complete", "error"]
    
    # 分析阶段结果
    workbook_info: Dict
    text_cells: Annotated[List[Dict], operator.add]
    
    # 翻译阶段结果
    translated_cells: Annotated[List[Dict], operator.add]
    translation_progress: int
    
    # 最终结果
    success: bool
    error_message: str
    result_summary: Dict

@dataclass
class LangGraphExcelTranslatorConfig:
    """LangGraph Excel翻译器配置"""
    target_language: str = "中文"
    batch_size: int = 10
    enable_checkpoints: bool = True  # 是否启用检查点

class LangGraphExcelTranslator:
    """基于LangGraph的Excel翻译器"""
    
    def __init__(self, config: LangGraphExcelTranslatorConfig):
        self.config = config
        
        if not LANGGRAPH_AVAILABLE:
            raise ImportError("LangGraph不可用，请安装: pip install langgraph")
        
        # 创建检查点保存器（用于状态持久化）
        self.checkpointer = MemorySaver() if config.enable_checkpoints else None
        
        # 构建工作流图
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流"""
        # 创建状态图
        workflow = StateGraph(ExcelTranslationState)
        
        # 添加节点
        workflow.add_node("analyze_excel", self._analyze_excel_node)
        workflow.add_node("translate_texts", self._translate_texts_node)
        workflow.add_node("save_excel", self._save_excel_node)
        workflow.add_node("handle_error", self._handle_error_node)
        
        # 设置入口点
        workflow.set_entry_point("analyze_excel")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "analyze_excel",
            self._should_continue_after_analyze,
            {
                "translate": "translate_texts",
                "error": "handle_error"
            }
        )
        
        workflow.add_conditional_edges(
            "translate_texts",
            self._should_continue_after_translate,
            {
                "save": "save_excel",
                "error": "handle_error"
            }
        )
        
        # 添加结束边
        workflow.add_edge("save_excel", END)
        workflow.add_edge("handle_error", END)
        
        # 编译图
        return workflow.compile(checkpointer=self.checkpointer)
    
    def _analyze_excel_node(self, state: ExcelTranslationState) -> ExcelTranslationState:
        """分析Excel文件节点"""
        print(f"[分析阶段] 正在分析Excel文件: {state['input_file']}")
        
        try:
            workbook = xlrd.open_workbook(state['input_file'])
            sheet_names = workbook.sheet_names()
            
            workbook_info = {
                "sheet_count": len(sheet_names),
                "sheet_names": sheet_names
            }
            
            text_cells = []
            
            # 遍历所有工作表
            for sheet_name in sheet_names:
                sheet = workbook.sheet_by_name(sheet_name)
                print(f"  分析工作表: {sheet_name} ({sheet.nrows}行 x {sheet.ncols}列)")
                
                # 遍历所有单元格
                for row in range(sheet.nrows):
                    for col in range(sheet.ncols):
                        try:
                            value = sheet.cell_value(row, col)
                            if isinstance(value, str) and len(value.strip()) > 0:
                                # 简单判断是否需要翻译
                                clean_value = value.strip()
                                if (not clean_value.replace('.', '').replace(',', '').replace('-', '').replace('%', '').isdigit() 
                                    and not clean_value.startswith('=')):
                                    text_cells.append({
                                        "sheet_name": sheet_name,
                                        "row": row,
                                        "col": col,
                                        "original_text": clean_value,
                                        "cell_ref": f"{sheet_name}!{chr(65 + col)}{row + 1}"
                                    })
                        except Exception:
                            continue
            
            print(f"[分析阶段] 找到 {len(text_cells)} 个需要翻译的文本单元格")
            
            return {
                **state,
                "current_step": "analyze",
                "workbook_info": workbook_info,
                "text_cells": text_cells,
                "translation_progress": 0
            }
            
        except Exception as e:
            print(f"[分析阶段] 分析Excel文件失败: {e}")
            return {
                **state,
                "current_step": "error",
                "success": False,
                "error_message": f"分析Excel文件失败: {e}"
            }
    
    def _translate_texts_node(self, state: ExcelTranslationState) -> ExcelTranslationState:
        """翻译文本节点"""
        text_cells = state.get("text_cells", [])
        print(f"[翻译阶段] 开始翻译 {len(text_cells)} 个文本单元格...")
        
        try:
            translated_cells = []
            batch_size = self.config.batch_size
            
            # 简单的翻译映射（模拟翻译）
            translation_map = {
                "PROJECT ECONOMIC ANALYSIS": "项目经济分析",
                "PROJECT": "项目",
                "COAL FIRED POWER PLANT": "燃煤发电厂",
                "Project Investment Cost": "项目投资成本",
                "Assessment Index": "评估指标",
                "Hard Costs": "硬成本",
                "Soft Costs": "软成本",
                "Construction": "建设",
                "Operation": "运营",
                "Financing": "融资",
                "Depreciation": "折旧",
                "Assumption": "假设",
                "All figures in USD '000 unless stated otherwise": "除非另有说明，所有数字均以千美元为单位",
                "Investment": "投资",
                "Income Statement": "损益表",
                "Cash Flow": "现金流",
                "Balance Sheet": "资产负债表",
                "Dividends": "股息",
                "Period Start": "期间开始",
                "Period End": "期间结束"
            }
            
            # 分批处理
            for i in range(0, len(text_cells), batch_size):
                batch = text_cells[i:i + batch_size]
                print(f"  [翻译阶段] 处理批次 {i//batch_size + 1}/{(len(text_cells) + batch_size - 1)//batch_size}")
                
                # 翻译每个单元格
                for cell in batch:
                    original_text = cell["original_text"]
                    
                    # 查找翻译
                    translated_text = translation_map.get(original_text, original_text)
                    
                    # 如果没有直接匹配，尝试部分匹配
                    if translated_text == original_text:
                        for en, zh in translation_map.items():
                            if en.lower() in original_text.lower():
                                translated_text = original_text.replace(en, zh)
                                break
                    
                    translated_cell = {
                        **cell,
                        "translated_text": translated_text
                    }
                    translated_cells.append(translated_cell)
            
            print(f"[翻译阶段] 翻译完成，共处理 {len(translated_cells)} 个单元格")
            
            return {
                **state,
                "current_step": "translate",
                "translated_cells": translated_cells,
                "translation_progress": 100
            }
            
        except Exception as e:
            print(f"[翻译阶段] 翻译失败: {e}")
            return {
                **state,
                "current_step": "error",
                "success": False,
                "error_message": f"翻译失败: {e}"
            }
    
    def _save_excel_node(self, state: ExcelTranslationState) -> ExcelTranslationState:
        """保存Excel文件节点"""
        print(f"[保存阶段] 正在保存翻译结果到: {state['output_file']}")
        
        try:
            # 读取原始文件
            original_workbook = xlrd.open_workbook(state['input_file'])
            
            # 创建新的工作簿
            new_workbook = xlwt.Workbook(encoding='utf-8')
            
            # 创建翻译映射
            translation_map = {}
            for cell in state.get("translated_cells", []):
                key = f"{cell['sheet_name']}_{cell['row']}_{cell['col']}"
                translation_map[key] = cell['translated_text']
            
            # 复制所有工作表
            for sheet_name in original_workbook.sheet_names():
                original_sheet = original_workbook.sheet_by_name(sheet_name)
                new_sheet = new_workbook.add_sheet(sheet_name)
                
                print(f"  [保存阶段] 处理工作表: {sheet_name}")
                
                # 复制所有单元格
                for row in range(original_sheet.nrows):
                    for col in range(original_sheet.ncols):
                        try:
                            original_value = original_sheet.cell_value(row, col)
                            
                            # 检查是否有翻译
                            key = f"{sheet_name}_{row}_{col}"
                            if key in translation_map:
                                new_value = translation_map[key]
                            else:
                                new_value = original_value
                            
                            # 写入新单元格
                            if isinstance(new_value, str):
                                new_sheet.write(row, col, new_value)
                            elif isinstance(new_value, (int, float)):
                                new_sheet.write(row, col, new_value)
                            else:
                                new_sheet.write(row, col, str(new_value))
                                
                        except Exception as e:
                            print(f"    [保存阶段] 警告：复制单元格 {row},{col} 失败: {e}")
                            continue
            
            # 保存文件
            new_workbook.save(state['output_file'])
            print(f"[保存阶段] 翻译完成！文件已保存到: {state['output_file']}")
            
            # 生成结果摘要
            result_summary = {
                "input_file": state['input_file'],
                "output_file": state['output_file'],
                "sheet_count": state['workbook_info']['sheet_count'],
                "translated_cell_count": len(state.get('translated_cells', [])),
                "target_language": state['target_language']
            }
            
            return {
                **state,
                "current_step": "complete",
                "success": True,
                "result_summary": result_summary
            }
            
        except Exception as e:
            print(f"[保存阶段] 保存Excel文件失败: {e}")
            return {
                **state,
                "current_step": "error",
                "success": False,
                "error_message": f"保存Excel文件失败: {e}"
            }
    
    def _handle_error_node(self, state: ExcelTranslationState) -> ExcelTranslationState:
        """错误处理节点"""
        print(f"[错误处理] {state.get('error_message', '未知错误')}")
        
        return {
            **state,
            "current_step": "error",
            "success": False
        }
    
    def _should_continue_after_analyze(self, state: ExcelTranslationState) -> str:
        """分析后的条件判断"""
        if state.get("current_step") == "error":
            return "error"
        elif state.get("text_cells"):
            return "translate"
        else:
            return "error"
    
    def _should_continue_after_translate(self, state: ExcelTranslationState) -> str:
        """翻译后的条件判断"""
        if state.get("current_step") == "error":
            return "error"
        elif state.get("translated_cells"):
            return "save"
        else:
            return "error"
    
    def translate_excel(self, input_file: str, output_file: str, target_language: str = "中文") -> Dict:
        """翻译Excel文件的主入口"""
        print(f"[工作流] 开始Excel翻译工作流...")
        print(f"[工作流] 输入文件: {input_file}")
        print(f"[工作流] 输出文件: {output_file}")
        print(f"[工作流] 目标语言: {target_language}")
        
        # 初始状态
        initial_state = {
            "input_file": input_file,
            "output_file": output_file,
            "target_language": target_language,
            "current_step": "analyze",
            "workbook_info": {},
            "text_cells": [],
            "translated_cells": [],
            "translation_progress": 0,
            "success": False,
            "error_message": "",
            "result_summary": {}
        }
        
        # 运行工作流
        try:
            # 如果启用了检查点，可以指定线程ID来恢复状态
            config = {"configurable": {"thread_id": "excel_translation_001"}} if self.checkpointer else {}
            final_state = self.graph.invoke(initial_state, config=config)
            
            print(f"[工作流] 工作流执行完成，当前步骤: {final_state.get('current_step', 'unknown')}")
            
            return final_state
            
        except Exception as e:
            print(f"[工作流] 工作流执行失败: {e}")
            return {
                **initial_state,
                "current_step": "error",
                "success": False,
                "error_message": f"工作流执行失败: {e}"
            }

def main():
    """主函数 - 演示LangGraph Excel翻译工作流"""
    if not LANGGRAPH_AVAILABLE:
        print("错误：LangGraph不可用，请安装: pip install langgraph")
        return
    
    # 配置
    config = LangGraphExcelTranslatorConfig(
        target_language="中文",
        batch_size=10,
        enable_checkpoints=True
    )
    
    # 创建翻译器
    translator = LangGraphExcelTranslator(config)
    
    # 文件路径
    input_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model.xls"
    output_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model_langgraph.xls"
    
    # 检查输入文件
    if not Path(input_file).exists():
        print(f"错误：输入文件不存在: {input_file}")
        return
    
    # 执行翻译
    result = translator.translate_excel(input_file, output_file, "中文")
    
    # 输出结果
    print("\n=== LangGraph工作流结果 ===")
    print(f"成功: {result.get('success', False)}")
    print(f"当前步骤: {result.get('current_step', 'unknown')}")
    
    if result.get('error_message'):
        print(f"错误: {result['error_message']}")
    
    if result.get('result_summary'):
        summary = result['result_summary']
        print(f"工作表数量: {summary.get('sheet_count', 0)}")
        print(f"翻译的单元格数量: {summary.get('translated_cell_count', 0)}")
        print(f"输出文件: {summary.get('output_file', 'N/A')}")

if __name__ == "__main__":
    main()
